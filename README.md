<div align="center">

<br/>
<p align="center">
  <img src="https://img.shields.io/badge/🧬_BASIS--China-iGEM_2025-4B5563?style=for-the-badge&labelColor=1F2937" alt="BASIS-China iGEM 2025"/>
</p>

# 🧬 BASIS-China iGEM 2025 Wiki

<p align="center">
  <em>A modern, performant wiki platform for synthetic biology innovation</em>
</p>

## 🌟 Presenting the **SnaPFAS** Project 🌟

<p align="center">
  <strong>Sustainable • Novel • Actionable • PFAS Degradation</strong>
</p>

<br/>

<!-- Tech Stack Badges -->
<p align="center">
  <a href="https://vuejs.org/"><img src="https://img.shields.io/badge/Vue.js-3.5.13-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white" alt="Vue.js"/></a>
  <a href="https://www.typescriptlang.org/"><img src="https://img.shields.io/badge/TypeScript-5.8.2-3178C6?style=for-the-badge&logo=typescript&logoColor=white" alt="TypeScript"/></a>
  <a href="https://vitejs.dev/"><img src="https://img.shields.io/badge/Vite-6.0.11-646CFF?style=for-the-badge&logo=vite&logoColor=white" alt="Vite"/></a>
  <a href="https://tailwindcss.com/"><img src="https://img.shields.io/badge/Tailwind_CSS-3.4.17-06B6D4?style=for-the-badge&logo=tailwindcss&logoColor=white" alt="Tailwind CSS"/></a>
</p>

<!-- Status Badges -->
<p align="center">
  <img src="https://img.shields.io/badge/Status-Active_Development-10B981?style=flat-square&logo=statuspage&logoColor=white" alt="Status"/>
  <img src="https://img.shields.io/badge/License-CC_BY_4.0-FCA121?style=flat-square&logo=creativecommons&logoColor=white" alt="License"/>
  <img src="https://img.shields.io/badge/Node.js-20.17+-339933?style=flat-square&logo=node.js&logoColor=white" alt="Node.js"/>
  <img src="https://img.shields.io/badge/Yarn-4.1.1-2C8EBB?style=flat-square&logo=yarn&logoColor=white" alt="Yarn"/>
</p>

<br/>

<p align="center">
  <strong>
    <a href="#-quick-start">🚀 Quick Start</a> •
    <a href="#-features">✨ Features</a> •
    <a href="#-project-structure">📁 Structure</a> •
    <a href="#-development">💻 Development</a>
  </strong>
</p>

<br/>

> 🏆 **Modern Architecture** • Built with cutting-edge web technologies featuring SEO optimization, smooth animations, and a professional design system - all optimized for iGEM wiki requirements.

</div>

---

## ✨ **Features**

### 🎨 **Implemented UI/UX Features**

<div align="center">
<table>
<tr>
<td width="50%" align="center">
<h3>🎯</h3>
<strong>Professional Navigation</strong><br/>
<sub>Multi-level dropdown menu with smooth transitions</sub>
</td>
<td width="50%" align="center">
<h3>📱</h3>
<strong>Fully Responsive</strong><br/>
<sub>Mobile-first design with smooth animations and transitions</sub>
</td>
</tr>
</table>
</div>

### 🚀 **New: SEO & Performance Features**

<div align="center">
<table>
<tr>
<td width="33%" align="center">
<h3>🔍</h3>
<strong>Advanced SEO System</strong><br/>
<sub>Dynamic meta tags, structured data, and automated sitemap generation</sub>
</td>
<td width="33%" align="center">
<h3>📊</h3>
<strong>Social Media Optimization</strong><br/>
<sub>Open Graph & Twitter Cards for enhanced sharing</sub>
</td>
<td width="33%" align="center">
<h3>🤖</h3>
<strong>Search Engine Ready</strong><br/>
<sub>JSON-LD schemas, robots.txt, and optimized crawling</sub>
</td>
</tr>
</table>
</div>

### ⚡ **Technical Implementation**

<div align="center">
<table>
<tr>
<td width="33%" align="center">
<h3>🏗️</h3>
<strong>Vue 3 Composition API</strong><br/>
<sub>Modern architecture with &lt;script setup&gt; and full TypeScript support</sub>
</td>
<td width="33%" align="center">
<h3>🎨</h3>
<strong>Design Token System</strong><br/>
<sub>Professional CSS custom properties with 8px grid system</sub>
</td>
<td width="33%" align="center">
<h3>⚡</h3>
<strong>Optimized Performance</strong><br/>
<sub>Code splitting, lazy loading, and vendor chunk optimization</sub>
</td>
</tr>
</table>
</div>

### 📝 **Content & Documentation Features**

<div align="center">
<table>
<tr>
<td width="25%" align="center">
<h3>📄</h3>
<strong>Markdown Processing</strong><br/>
<sub>Advanced parser with frontmatter, auto-TOC, and custom plugins</sub>
</td>
<td width="25%" align="center">
<h3>🎨</h3>
<strong>Syntax Highlighting</strong><br/>
<sub>Prism.js with 20+ languages and one-click copy</sub>
</td>
<td width="25%" align="center">
<h3>📊</h3>
<strong>Mermaid Diagrams</strong><br/>
<sub>Flowcharts, sequence diagrams, and Gantt charts</sub>
</td>
<td width="25%" align="center">
<h3>📐</h3>
<strong>Math Equations</strong><br/>
<sub>KaTeX rendering for LaTeX math notation</sub>
</td>
</tr>
</table>
</div>

### 🚧 **Development Status**

<div align="center">

| Component | Status | Description |
|:----------|:------:|:------------|
| **Navigation System** | ✅ Complete | Clean navigation structure with dropdown menus |
| **Page Routing** | ✅ Complete | 15 routes with Markdown content support |
| **Design System** | ✅ Complete | Comprehensive tokens and utilities |
| **UI Component Library** | ✅ Complete | Shadcn-Vue inspired components with CVA variants |
| **SEO Implementation** | ✅ Complete | Meta tags, sitemap, structured data |
| **Math Rendering** | ✅ Complete | KaTeX integrated with full LaTeX support |
| **Syntax Highlighting** | ✅ Complete | Prism.js with 20+ languages and copy buttons |
| **Mermaid Diagrams** | ✅ Complete | Flowcharts, sequence diagrams, and Gantt charts |
| **Markdown Processing** | ✅ Complete | Advanced parser with TOC, frontmatter, and plugins |
| **Content System** | ✅ Complete | Markdown-based articles with metadata |
| **State Management** | 📋 Ready | Pinia installed and configured, stores not implemented |

</div>

---

## 🚀 **Quick Start**

```bash
# Clone the repository
git clone https://gitlab.igem.org/2025/basis-china.git

# Navigate to project
cd basis-china

# Install dependencies (Yarn 4.1.1 with PnP)
yarn install

# Start development server
yarn dev

# Open http://localhost:5173
```

---

## 💻 **Development**

### 📜 **Available Scripts**

| Command | Description |
|:--------|:------------|
| `yarn dev` | Start development server on localhost:5173 |
| `yarn build` | Production build with type checking |
| `yarn build:fast` | Quick build without type checking |
| `yarn preview` | Preview production build |
| `yarn lint` | Run ESLint + OxLint with auto-fix |
| `yarn format` | Format code with Prettier |
| `yarn type-check` | TypeScript validation |
| `yarn check-all` | Run all quality checks |
| `yarn fix-all` | Auto-fix all issues |
| `yarn clean` | Clean build artifacts |
| `yarn build:analyze` | Analyze bundle size |
| `yarn generate:sitemap` | Generate sitemap.xml (auto-runs before build) |

### 🏃 **Development Workflow**

```bash
# 1. Start development
yarn dev

# 2. Before committing
yarn check-all

# 3. Fix any issues
yarn fix-all

# 4. Build for production
yarn build
```

### 🔍 **SEO Workflow**

The project includes comprehensive SEO optimization:

1. **Automatic Meta Tags** - All routes have configured meta tags via `useSEO` composable
2. **Sitemap Generation** - Automatically generated before each build
3. **Structured Data** - JSON-LD schemas for different page types
4. **Social Sharing** - Open Graph and Twitter Card meta tags

See `src/docs/SEO-OPTIMIZATION.md` for detailed SEO documentation.

### 📝 **Content Management**

Write content in Markdown with advanced features:

1. **Create Article** - Add `.md` files to `src/content/articles/`
2. **Frontmatter** - Include metadata at the top of files:
   ```yaml
   ---
   title: Article Title
   description: Brief description
   author: Author Name
   date: 2025-01-14
   tags: [tag1, tag2]
   ---
   ```
3. **Features Available**:
   - **Math Equations** - Use `$...$` for inline and `$$...$$` for block equations
   - **Code Blocks** - Syntax highlighting with language support
   - **Mermaid Diagrams** - Create flowcharts with ` ```mermaid` blocks
   - **Auto-TOC** - Table of contents generated from headings
   - **Responsive Images** - Automatically optimized for all devices

---

## 📁 **Project Structure**

```
basis-china/
├── 📁 src/
│   ├── 📁 components/           # Vue components
│   │   ├── ArticleTableOfContents.vue  # Auto-generated TOC
│   │   ├── Footer.vue                 # Site footer
│   │   ├── KatexMath.vue              # Math equation rendering
│   │   ├── Navbar.vue                 # Navigation component
│   │   ├── PageTransition.vue         # Route transitions
│   │   └── 📁 ui/                     # UI component library
│   │       ├── AnimatedNumber.vue     # Number animations
│   │       ├── Badge.vue              # Badge component
│   │       ├── Button.vue             # Button with variants
│   │       ├── Card.vue               # Card container
│   │       ├── CardContent.vue        # Card content section
│   │       ├── CardHeader.vue         # Card header section
│   │       └── CardTitle.vue          # Card title component
│   │
│   ├── 📁 composables/          # Composition utilities
│   │   ├── useSEO.ts                  # SEO meta tag management
│   │   └── useMarkdownContent.ts      # Markdown content loading
│   │
│   ├── 📁 content/              # Markdown content
│   │   └── 📁 articles/                # Article markdown files
│   │       ├── 📁 dry-lab/             # Dry lab documentation
│   │       │   ├── hardware.md
│   │       │   ├── model.md
│   │       │   └── software.md
│   │       ├── 📁 wet-lab/             # Wet lab documentation
│   │       │   ├── engineering.md
│   │       │   ├── experiment.md
│   │       │   ├── parts.md
│   │       │   ├── results.md
│   │       │   └── safety.md
│   │       ├── experimental-results.md # Results with math & diagrams
│   │       ├── human-practices.md      # Community engagement
│   │       ├── project-design.md       # Design documentation
│   │       └── project-overview.md     # Project introduction
│   │
│   ├── 📁 data/                 # Static data
│   │   ├── teamMembers.ts             # Team information with photos
│   │   └── seoConfig.ts               # SEO metadata for all routes
│   │
│   ├── 📁 docs/                 # Documentation
│   │   └── SEO-OPTIMIZATION.md        # Complete SEO guide
│   │
│   ├── 📁 lib/                  # Utility functions
│   │   └── utils.ts                   # Class name merging utility (cn)
│   │
│   ├── 📁 plugins/              # Vue plugins
│   │   ├── katex.ts                   # KaTeX math rendering
│   │   ├── mermaid.ts                 # Mermaid diagram support
│   │   └── prism-simple.ts            # Syntax highlighting with Prism.js
│   │
│   ├── 📁 router/               # Routing configuration
│   │   ├── index.ts                   # Router setup with SEO
│   │   └── routes.ts                  # Route definitions (15 routes)
│   │
│   ├── 📁 scripts/              # Build scripts
│   │   └── generate-sitemap.ts        # Sitemap generation script
│   │
│   ├── 📁 services/             # Service utilities
│   │   └── markdownService.ts         # Markdown processing with plugins
│   │
│   ├── 📁 stores/               # State management (Pinia ready)
│   │   └── (empty - stores created as needed)
│   │
│   ├── 📁 styles/               # Design system
│   │   ├── design-tokens.css          # CSS custom properties
│   │   ├── design-system.md           # Design documentation
│   │   └── visual-design-guide.md     # Visual guidelines
│   │
│   ├── 📁 types/                # TypeScript definitions
│   │
│   ├── 📁 views/                # Page components
│   │   ├── HomeView.vue                # Landing page
│   │   ├── MarkdownPage.vue            # Dynamic markdown content view
│   │   ├── TeamMembers.vue             # Team page with photos
│   │   ├── Attribution.vue             # Credits page
│   │   └── NotFound.vue                # 404 page with video
│   │
│   ├── App.vue                  # Root component
│   └── main.ts                  # Application entry with Pinia setup
│
├── 📁 public/                   # Static assets
│   ├── robots.txt              # Search engine directives
│   ├── sitemap.xml             # Generated sitemap
│   ├── 404.webm                # 404 page video
│   └── logo.webp               # Project logo
│
├── 📄 Configuration Files
│   ├── vite.config.ts          # Vite configuration
│   ├── tsconfig.json           # TypeScript config
│   ├── tailwind.config.js      # Tailwind config
│   └── eslint.config.ts        # ESLint config
│
└── 📄 Project Files
    ├── .gitlab-ci.yml          # GitLab CI/CD pipeline
    ├── package.json            # Dependencies & scripts
    ├── CLAUDE.md               # AI assistant guidelines
    └── .env                    # Environment variables
```

---

## 🛠️ **Tech Stack Details**

### Core Framework
- **Vue 3.5.13** - Progressive JavaScript framework with Composition API
- **TypeScript 5.8.2** - Type-safe development with strict mode
- **Vite 6.0.11** - Lightning-fast build tool with HMR
- **Vue Router 4.5.0** - Official router with 17 configured routes

### Styling & Design
- **Tailwind CSS 3.4.17** - Utility-first CSS framework
- **CSS Custom Properties** - Comprehensive design token system
- **PostCSS** - CSS processing with autoprefixer

### UI Libraries
- **Ant Design Vue 4.2.6** - Enterprise-class components
- **Headless UI 1.7.23** - Unstyled accessible components
- **Heroicons 2.2.0** - Beautiful hand-crafted icons
- **Lucide Vue Next 0.525.0** - Modern icon library
- **Radix Vue 1.9.17** - Headless UI primitives
- **Class Variance Authority 0.7.1** - Component variant management
- **Tailwind Merge 3.3.1** - Intelligent class merging

### Animation & Effects
- **GSAP 3.12.7** - Professional-grade animation library
- **AOS 2.3.4** - Animate on scroll library
- **Page Transitions** - Smooth fade effects between routes

### Development Tools
- **ESLint 9.19.0** - JavaScript linting
- **OxLint 0.11.1** - Fast Rust-based linter
- **Prettier 3.4.2** - Code formatting
- **Vue DevTools 7.7.1** - Browser debugging
- **tsx 4.20.3** - TypeScript execution for scripts

### Content & Utilities
- **KaTeX 0.16.21** - Fast math typesetting for LaTeX equations
- **Markdown-it 14.1.0** - Advanced markdown parser
- **Markdown-it-anchor 9.2.0** - Auto-generate heading anchors
- **Mermaid 11.9.0** - Diagram and flowchart rendering
- **Prism.js 1.30.0** - Syntax highlighting with 20+ languages
- **VueUse 12.5.0** - Collection of Vue composition utilities
- **Pinia 2.3.0** - State management (installed and configured, stores not implemented)

---

## ⚙️ **Configuration**

### Environment Variables

Create a `.env` file in the root directory:

```env
# Team Configuration
VITE_TEAM_NAME=basis-china
VITE_TEAM_YEAR=2025
VITE_TEAM_ID=5610

# API Configuration (for future use)
VITE_API_URL=https://api.igem.org
VITE_API_KEY=your-api-key
```

### Build Configuration

The project uses optimized chunk splitting:
- `vue-vendor` - Vue core libraries
- `ui-vendor` - UI component libraries
- `utils-vendor` - Utility libraries
- `markdown-vendor` - Content processing

### Design System

Professional design tokens in `src/styles/design-tokens.css`:
- **Spacing**: 8px grid system (--space-xs through --space-6xl)
- **Typography**: Extended type scale with fluid responsive sizing
- **Colors**: 5-level semantic color system with dark/light mode support
- **Shadows**: Layered shadow system for depth
- **Animation**: Consistent timing (150ms-800ms) and easing functions

---

## 🚀 **Deployment**

The project includes GitLab CI/CD configuration for automatic deployment:

```yaml
# Automatic deployment on push to main
# Builds and deploys to GitLab Pages
# Available at: https://2025.igem.wiki/basis-china/
```

### Deployment Features:
- **Automated Builds** - Push to main triggers deployment
- **Optimized Caching** - Fast CI/CD with Yarn cache
- **Sitemap Generation** - Automatic sitemap.xml creation
- **Production Optimization** - Minified, tree-shaken builds

---

## 📄 **License**

<div align="center">

<img src="https://img.shields.io/badge/License-CC_BY_4.0-FCA121?style=for-the-badge&logo=creativecommons&logoColor=white" alt="CC BY 4.0"/>

This project is licensed under the [Creative Commons Attribution 4.0 International License](https://creativecommons.org/licenses/by/4.0/)

</div>

---

<div align="center">

### 🙏 **Acknowledgments**

Built with ❤️ by **BASIS-China iGEM Team 2025**

Special thanks to the iGEM Foundation and all open-source contributors.

<br/>

**Last Updated:** 2025-08-15

<br/>

<a href="#-basis-china-igem-2025-wiki">
<img src="https://img.shields.io/badge/⬆_Back_to_Top-1F2937?style=for-the-badge" alt="Back to Top"/>
</a>

</div>