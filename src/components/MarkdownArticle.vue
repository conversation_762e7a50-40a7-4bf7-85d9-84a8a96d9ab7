<template>
  <div class="markdown-article-container pt-8">
    <!-- Header -->
    <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
      <h1
        v-if="meta.title"
        class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900"
      >
        {{ meta.title }}
      </h1>
      <div
        v-if="meta.title"
        class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
      ></div>
      <p
        v-if="meta.description"
        class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed"
      >
        {{ meta.description }}
      </p>

      <!-- Meta information -->
      <div
        v-if="meta.author || meta.date || meta.tags"
        class="flex flex-wrap justify-center gap-4 mt-6 text-sm text-gray-500"
      >
        <span v-if="meta.author" class="flex items-center gap-1">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" />
          </svg>
          {{ meta.author }}
        </span>
        <span v-if="meta.date" class="flex items-center gap-1">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
              clip-rule="evenodd"
            />
          </svg>
          {{ formatDate(meta.date) }}
        </span>
        <div v-if="meta.tags && meta.tags.length" class="flex gap-2">
          <span
            v-for="tag in meta.tags"
            :key="tag"
            class="px-2 py-1 bg-gray-100 rounded-full text-xs"
          >
            {{ tag }}
          </span>
        </div>
      </div>
    </div>

    <!-- Mobile Table of Contents -->
    <div v-if="showToc && tocItems.length > 0" class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
      <ArticleTableOfContents :items="tocItems" :title="tocTitle" :default-open="false" />
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
      <!-- Desktop Table of Contents -->
      <div
        v-if="showToc && tocItems.length > 0"
        class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4"
      >
        <ArticleTableOfContents :items="tocItems" :title="tocTitle" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
        <article
          class="markdown-content max-w-4xl bg-white rounded-xl p-6 sm:p-8 shadow-sm"
          v-html="htmlContent"
        ></article>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import { processMermaidDiagrams } from '@/plugins/mermaid'
import { initCodeCopyButtons } from '@/plugins/prism-simple'
import {
  loadMarkdownContent,
  processMarkdown,
  type MarkdownMeta,
  type TocItem,
} from '@/services/markdownService'
import { nextTick, onMounted, ref, watch } from 'vue'

interface MarkdownArticleProps {
  // Static content (if provided directly)
  content?: string
  // Path to markdown file (for dynamic loading)
  contentPath?: string
  // Override meta from props
  metaOverride?: Partial<MarkdownMeta>
  // Show table of contents
  showToc?: boolean
  // TOC title
  tocTitle?: string
}

const props = withDefaults(defineProps<MarkdownArticleProps>(), {
  content: undefined,
  contentPath: undefined,
  metaOverride: undefined,
  showToc: true,
  tocTitle: 'Contents',
})

// State
const htmlContent = ref('')
const meta = ref<MarkdownMeta>({})
const tocItems = ref<TocItem[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// Format date for display
const formatDate = (date: string | Date) => {
  if (!date) return ''
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

// Process content
const processContent = async (content: string) => {
  try {
    loading.value = true
    error.value = null

    const processed = processMarkdown(content)

    htmlContent.value = processed.html
    meta.value = { ...processed.meta, ...props.metaOverride }
    tocItems.value = processed.toc

    // Wait for DOM update then process Mermaid diagrams
    await nextTick()
    const contentEl = document.querySelector('.markdown-content')
    if (contentEl) {
      processMermaidDiagrams(contentEl as HTMLElement)
    }
  } catch (err) {
    console.error('Error processing markdown:', err)
    error.value = err instanceof Error ? err.message : 'Failed to process markdown'
  } finally {
    loading.value = false
  }
}

// Load content from path
const loadContent = async () => {
  if (!props.contentPath) return

  try {
    loading.value = true
    error.value = null

    const content = await loadMarkdownContent(props.contentPath)
    await processContent(content)
  } catch (err) {
    console.error('Error loading markdown:', err)
    error.value = err instanceof Error ? err.message : 'Failed to load markdown'
  } finally {
    loading.value = false
  }
}

// Watch for content changes
watch(
  () => props.content,
  newContent => {
    if (newContent) {
      processContent(newContent)
    }
  },
  { immediate: true }
)

// Watch for content path changes
watch(
  () => props.contentPath,
  () => {
    if (props.contentPath && !props.content) {
      loadContent()
    }
  },
  { immediate: true }
)

// Load content on mount if needed
onMounted(() => {
  // Initialize code copy buttons
  initCodeCopyButtons()

  if (!props.content && props.contentPath) {
    loadContent()
  } else if (props.content) {
    processContent(props.content)
  }
})
</script>

<style scoped>
.markdown-article-container {
  min-height: calc(100vh - 64px);
  background-color: rgb(249, 250, 251);
  padding-top: 4rem;
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .markdown-article-container {
    padding-top: 5rem;
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px) {
  .markdown-article-container {
    padding-top: 6rem;
    padding-bottom: 4rem;
  }
}

/* Enhanced focus states */
a:focus-visible,
button:focus-visible {
  outline: 2px solid var(--color-primary-400);
  outline-offset: 2px;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}
</style>

<style>
/* Global styles for markdown content */
.markdown-content {
  @apply prose prose-lg max-w-none;
  line-height: 1.7;
}

.markdown-content h1 {
  @apply text-3xl font-bold mt-8 mb-4 text-gray-900 border-b-2 border-gray-200 pb-2;
}

.markdown-content h2 {
  @apply text-2xl font-bold mt-8 mb-4 text-gray-900 flex items-center gap-3;
  padding-top: 1rem;
}

.markdown-content h2::before {
  content: '';
  @apply w-1 h-8 bg-primary-500 rounded-full;
}

.markdown-content h3 {
  @apply text-xl font-semibold mt-6 mb-3 text-gray-800;
  padding-left: 0.5rem;
  border-left: 3px solid rgb(209 213 219);
}

.markdown-content h4 {
  @apply text-lg font-medium mt-4 mb-2 text-gray-700;
  padding-left: 0.75rem;
}

.markdown-content p {
  @apply text-base text-gray-700 mb-4 leading-relaxed;
  line-height: 1.75;
}

.markdown-content ul,
.markdown-content ol {
  @apply mb-4 pl-6;
}

.markdown-content ul {
  @apply list-none space-y-2;
}

.markdown-content ul li {
  @apply flex items-start;
  transition: all 0.2s ease-out;
}

.markdown-content ul li::before {
  content: '';
  @apply text-primary-500 mr-3 mt-1 flex-shrink-0;
  display: inline-block;
  width: 20px;
  height: 20px;
  background: currentColor;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='currentColor' viewBox='0 0 20 20'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' clip-rule='evenodd'/%3E%3C/svg%3E")
    no-repeat;
  mask-size: contain;
}

.markdown-content ol {
  @apply list-decimal;
}

.markdown-content ol li {
  @apply text-gray-700;
}

.markdown-content a {
  @apply text-primary-600 hover:text-primary-700 underline transition-colors;
}

.markdown-content blockquote {
  @apply border-l-4 border-primary-400 pl-4 py-2 my-4 bg-gray-50 italic;
}

.markdown-content code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
}

.markdown-content pre {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto my-4;
}

.markdown-content pre code {
  @apply bg-transparent p-0;
}

.markdown-content table {
  @apply w-full border-collapse my-4;
}

.markdown-content th {
  @apply border border-gray-300 px-4 py-2 bg-gray-100 font-semibold text-left;
}

.markdown-content td {
  @apply border border-gray-300 px-4 py-2;
}

.markdown-content img {
  @apply max-w-full h-auto rounded-lg shadow-md my-4;
}

.markdown-content hr {
  @apply my-8 border-gray-300;
}

/* Math block styles */
.markdown-content .math-block {
  @apply my-6 overflow-x-auto;
  text-align: center;
}

.markdown-content .katex-display {
  @apply my-4;
}

/* Inline math */
.markdown-content .katex {
  @apply text-inherit;
}

/* Section styles */
.markdown-content section {
  @apply mb-8;
}

/* List item hover effects */
.markdown-content ul li:hover {
  color: rgb(17, 24, 39); /* text-gray-900 */
}

/* Add anchor link hover effect for headings */
.markdown-content h1[id],
.markdown-content h2[id],
.markdown-content h3[id],
.markdown-content h4[id] {
  position: relative;
  scroll-margin-top: 5rem;
}

.markdown-content h1[id]:hover,
.markdown-content h2[id]:hover,
.markdown-content h3[id]:hover,
.markdown-content h4[id]:hover {
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .markdown-content {
    @apply prose-base;
  }

  .markdown-content h1 {
    @apply text-2xl;
  }

  .markdown-content h2 {
    @apply text-xl;
  }

  .markdown-content h3 {
    @apply text-lg;
  }
}

/* Code block styles */
.markdown-content .code-block-wrapper {
  @apply my-6 rounded-lg overflow-hidden shadow-lg;
  background: #282c34;
}

.markdown-content .code-block-header {
  @apply flex justify-between items-center px-4 py-2 bg-gray-800 text-gray-300 text-sm;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.markdown-content .code-block-language {
  @apply font-medium;
}

.markdown-content .code-block-copy {
  @apply flex items-center gap-2 px-3 py-1 rounded bg-gray-700 hover:bg-gray-600 transition-colors cursor-pointer;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.markdown-content .code-block-copy:hover {
  border-color: rgba(255, 255, 255, 0.2);
}

.markdown-content .code-block-wrapper pre {
  @apply m-0 p-4 overflow-x-auto;
  background: #282c34;
  max-height: 600px;
}

.markdown-content .code-block-wrapper pre code {
  @apply bg-transparent p-0 text-sm;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  line-height: 1.6;
}

/* Line numbers */
.markdown-content pre.line-numbers {
  position: relative;
  padding-left: 3.8em;
  counter-reset: linenumber;
}

.markdown-content pre.line-numbers > code {
  position: relative;
}

.markdown-content .line-numbers-rows {
  position: absolute;
  pointer-events: none;
  top: 1em;
  font-size: 100%;
  left: -3.8em;
  width: 3em;
  letter-spacing: -1px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  user-select: none;
}

.markdown-content .line-numbers-rows > span {
  display: block;
  counter-increment: linenumber;
}

.markdown-content .line-numbers-rows > span:before {
  content: counter(linenumber);
  color: #6c757d;
  display: block;
  padding-right: 0.8em;
  text-align: right;
}

/* Mermaid diagram styles */
.markdown-content .mermaid-wrapper {
  @apply my-6;
}

.markdown-content .mermaid-container {
  @apply bg-white p-4 rounded-lg shadow-md overflow-x-auto;
  border: 1px solid #e5e7eb;
}

.markdown-content .mermaid-loading {
  @apply text-gray-500 text-center py-8;
}

.markdown-content .mermaid-error {
  @apply bg-red-50 text-red-600 p-4 rounded-lg;
  border: 1px solid #fca5a5;
}

/* Ensure mermaid diagrams are responsive */
.markdown-content .mermaid-container svg {
  max-width: 100%;
  height: auto;
}

/* Inline code styles */
.markdown-content :not(pre) > code {
  @apply bg-gray-100 text-red-600 px-2 py-0.5 rounded text-sm font-mono;
  border: 1px solid #e5e7eb;
}

/* Remove pseudo backticks added by Tailwind Typography for inline code */
.markdown-content :not(pre) > code::before,
.markdown-content :not(pre) > code::after {
  content: '' !important;
}

/* Diff highlighting */
.markdown-content .diff-highlight .token.deleted:not(.prefix) {
  background-color: rgba(239, 68, 68, 0.15);
}

.markdown-content .diff-highlight .token.inserted:not(.prefix) {
  background-color: rgba(34, 197, 94, 0.15);
}

.markdown-content .diff-highlight .token.coord {
  color: #a78bfa;
}
</style>
