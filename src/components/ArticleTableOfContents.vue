<template>
  <div class="article-toc-container">
    <!-- Mobile dropdown menu -->
    <div class="block md:hidden bg-white rounded-lg border border-slate-200 shadow-sm overflow-hidden">
      <button
        class="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-slate-50 transition-colors"
        @click="mobileNavOpen = !mobileNavOpen"
      >
        <span class="font-medium text-slate-800">{{ title }}</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 text-slate-500 transform transition-transform duration-200"
          :class="{ 'rotate-180': mobileNavOpen }"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
      <div v-if="mobileNavOpen" class="border-t border-slate-100">
        <nav class="py-2">
          <ul>
            <template v-for="(item, index) in items" :key="index">
              <li>
                <a
                  :href="item.url"
                  class="block py-2 px-4 text-sm transition-colors"
                  :class="{
                    'bg-primary-50 text-primary-700 font-medium': isActive(item.url),
                    'text-slate-600 hover:bg-slate-50': !isActive(item.url),
                  }"
                  :style="{ paddingLeft: `${Math.max(0, item.depth - 1) * 0.75 + 1}rem` }"
                  @click="handleItemClick"
                >
                  {{ item.title }}
                </a>
              </li>
              <!-- Children -->
              <template v-if="item.children && item.children.length > 0">
                <li v-for="(child, childIndex) in item.children" :key="`${index}-${childIndex}`">
                  <a
                    :href="child.url"
                    class="block py-1.5 px-4 text-sm transition-colors"
                    :class="{
                      'bg-primary-50/60 text-primary-600': isActive(child.url),
                      'text-slate-500 hover:bg-slate-50': !isActive(child.url),
                    }"
                    :style="{ paddingLeft: `${Math.max(0, child.depth - 1) * 0.75 + 1}rem` }"
                    @click="handleItemClick"
                  >
                    {{ child.title }}
                  </a>
                </li>
              </template>
            </template>
          </ul>
        </nav>
      </div>
    </div>

    <!-- Desktop sidebar -->
    <div class="hidden md:block w-full max-w-xs">
      <div class="toc-card bg-white rounded-xl border border-slate-200 shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden">
        <div class="px-4 py-3 border-b border-slate-100 bg-slate-50/50">
          <h2 class="font-medium text-slate-800">{{ title }}</h2>
        </div>
        <nav class="py-2" role="navigation" aria-label="Table of contents">
          <ul>
            <template v-for="(item, index) in items" :key="index">
              <li>
                <a
                  :href="item.url"
                  class="toc-item block py-2 px-4 text-sm transition-all duration-200 border-l-2"
                  :class="{
                    'border-primary-500 bg-primary-50 text-primary-700 font-medium': isActive(item.url),
                    'border-transparent text-slate-600 hover:text-slate-900 hover:bg-slate-50': !isActive(item.url),
                  }"
                  :style="{ paddingLeft: `${Math.max(0, item.depth - 1) * 0.75 + 1}rem` }"
                  @click="handleItemClick"
                >
                  {{ item.title }}
                </a>
              </li>
              <!-- Children -->
              <template v-if="item.children && item.children.length > 0">
                <li v-for="(child, childIndex) in item.children" :key="`${index}-${childIndex}`">
                  <a
                    :href="child.url"
                    class="toc-item block py-1.5 px-4 text-sm transition-all duration-200 border-l-2"
                    :class="{
                      'border-primary-400 bg-primary-50/60 text-primary-600': isActive(child.url),
                      'border-transparent text-slate-500 hover:text-slate-700 hover:bg-slate-50/50': !isActive(child.url),
                    }"
                    :style="{ paddingLeft: `${Math.max(0, child.depth - 1) * 0.75 + 1}rem` }"
                    @click="handleItemClick"
                  >
                    {{ child.title }}
                  </a>
                  <!-- Grandchildren -->
                  <template v-if="child.children && child.children.length > 0">
                    <ul>
                      <li v-for="(grandchild, gcIndex) in child.children" :key="`${index}-${childIndex}-${gcIndex}`">
                        <a
                          :href="grandchild.url"
                          class="block py-1 px-4 text-xs transition-colors duration-200"
                          :class="{
                            'text-primary-500': isActive(grandchild.url),
                            'text-slate-400 hover:text-slate-600': !isActive(grandchild.url),
                          }"
                          :style="{ paddingLeft: `${Math.max(0, grandchild.depth - 1) * 0.75 + 1}rem` }"
                          @click="handleItemClick"
                        >
                          {{ grandchild.title }}
                        </a>
                      </li>
                    </ul>
                  </template>
                </li>
              </template>
            </template>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'

interface TocItem {
  title: string
  url: string
  depth: number
  children?: TocItem[]
}

interface ArticleTocProps {
  items: TocItem[]
  title?: string
  defaultOpen?: boolean
}

const props = withDefaults(defineProps<ArticleTocProps>(), {
  items: () => [],
  title: '目录',
  defaultOpen: false,
})

const mobileNavOpen = ref(props.defaultOpen)
const activeSection = ref('')

// Check if link is active
const isActive = (url: string): boolean => {
  return activeSection.value === url.replace('#', '')
}

// Handle click events
const handleItemClick = (): void => {
  // Close menu on mobile
  if (window.innerWidth < 768) {
    setTimeout(() => {
      mobileNavOpen.value = false
    }, 300)
  }
}

// Listen to scroll to update active section
const updateActiveSection = (): void => {
  const sections = props.items
    .map(item => {
      const id = item.url.replace('#', '')
      const element = document.getElementById(id)
      if (element) {
        const rect = element.getBoundingClientRect()
        return {
          id,
          top: rect.top,
          bottom: rect.bottom,
        }
      }
      return null
    })
    .filter(Boolean) as { id: string; top: number; bottom: number }[]

  // Find the section currently in viewport
  const viewportHeight = window.innerHeight
  const currentSection = sections.find(section => {
    return section.top <= viewportHeight * 0.3 && section.bottom >= 0
  })

  if (currentSection) {
    activeSection.value = currentSection.id
  }
}

// Debounce function
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(later, wait)
  }
}

const debouncedUpdateActiveSection = debounce(updateActiveSection, 100)

onMounted(() => {
  window.addEventListener('scroll', debouncedUpdateActiveSection)
  updateActiveSection()
})

onUnmounted(() => {
  window.removeEventListener('scroll', debouncedUpdateActiveSection)
})
</script>

<style scoped>
.article-toc-container {
  position: relative;
}

@media (min-width: 768px) {
  .article-toc-container {
    position: sticky;
    top: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

/* Clean card style */
.toc-card {
  position: relative;
}

/* Subtle hover effect for items */
.toc-item {
  position: relative;
  transition: all 0.2s ease;
}

/* Custom scrollbar */
.article-toc-container::-webkit-scrollbar {
  width: 4px;
}

.article-toc-container::-webkit-scrollbar-track {
  background: transparent;
}

.article-toc-container::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 2px;
}

.article-toc-container::-webkit-scrollbar-thumb:hover {
  background: #cbd5e1;
}

/* Firefox scrollbar */
.article-toc-container {
  scrollbar-width: thin;
  scrollbar-color: #e2e8f0 transparent;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
a:focus-visible {
  outline: 2px solid var(--color-primary-400);
  outline-offset: 2px;
  border-radius: 0.25rem;
}
</style>