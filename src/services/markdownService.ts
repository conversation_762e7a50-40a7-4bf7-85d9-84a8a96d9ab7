import MarkdownIt from 'markdown-it'
import markdownItAnchor from 'markdown-it-anchor'
import { renderMath } from '@/plugins/katex'
import { highlightCode } from '@/plugins/prism-simple'
import { renderMermaidSync } from '@/plugins/mermaid'

export interface MarkdownMeta {
  title?: string
  description?: string
  author?: string
  date?: string
  tags?: string[]
  [key: string]: any
}

export interface ProcessedMarkdown {
  html: string
  meta: MarkdownMeta
  toc: TocItem[]
}

export interface TocItem {
  title: string
  url: string
  depth: number
  children?: TocItem[]
}

// Create markdown parser with plugins
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  breaks: false,
})

// Add anchor plugin for heading IDs
md.use(markdownItAnchor, {
  permalink: false,
  permalinkBefore: false,
  level: [1, 2, 3, 4],
  slugify: (s: string) => {
    return s
      .toLowerCase()
      .trim()
      .replace(/[\s+]/g, '-')
      .replace(/[^\w-]+/g, '')
      .replace(/--+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '')
  },
})

// Store TOC items
let tocItems: TocItem[] = []
let headingTokens: any[] = []

// Custom renderer to capture headings
const defaultHeadingOpen = md.renderer.rules.heading_open
md.renderer.rules.heading_open = function(tokens, idx, options, env, renderer) {
  const token = tokens[idx]
  const nextToken = tokens[idx + 1]
  const level = parseInt(token.tag.slice(1)) // h1 -> 1, h2 -> 2, etc.
  
  if (level >= 1 && level <= 4 && nextToken && nextToken.type === 'inline') {
    const title = nextToken.content
    const id = token.attrGet('id') || ''
    
    // Store heading info for TOC
    headingTokens.push({
      title,
      url: `#${id}`,
      depth: level
    })
  }
  
  // Call default renderer
  if (defaultHeadingOpen) {
    return defaultHeadingOpen(tokens, idx, options, env, renderer)
  } else {
    return renderer.renderToken(tokens, idx, options)
  }
}

// Build TOC from flat list to tree
function buildTocTree(flatItems: TocItem[]): TocItem[] {
  const result: TocItem[] = []
  const stack: { item: TocItem; depth: number }[] = []
  
  for (const item of flatItems) {
    // Remove items from stack that are deeper than current
    while (stack.length > 0 && stack[stack.length - 1].depth >= item.depth) {
      stack.pop()
    }
    
    if (stack.length === 0) {
      // Top level item
      result.push(item)
    } else {
      // Child of last stack item
      const parent = stack[stack.length - 1].item
      if (!parent.children) {
        parent.children = []
      }
      parent.children.push(item)
    }
    
    stack.push({ item, depth: item.depth })
  }
  
  return result
}

// Custom renderer for inline code - ensure clean output
md.renderer.rules.code_inline = function (tokens, idx) {
  const token = tokens[idx]
  let code = token.content
  
  // Force remove any backticks that shouldn't be there
  code = code.replace(/`/g, '')
  
  // Escape HTML entities
  const escapedCode = code
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
  
  return `<code>${escapedCode}</code>`
}

// Custom renderer for code blocks, math blocks, and mermaid diagrams
const defaultFence = md.renderer.rules.fence!
md.renderer.rules.fence = function (tokens, idx, options, env, renderer) {
  const token = tokens[idx]
  const info = token.info || ''
  const language = info.split(/\s+/)[0]

  // Check if it's a math block
  if (language === 'math' || language === 'latex') {
    const mathExpression = token.content
    return `<div class="math-block">${renderMath(mathExpression, true)}</div>`
  }

  // Check if it's a mermaid diagram
  if (language === 'mermaid') {
    return renderMermaidSync(token.content)
  }

  // Use Prism for syntax highlighting
  if (language && language !== '') {
    return highlightCode(token.content, language)
  }

  return defaultFence(tokens, idx, options, env, renderer)
}

// Process inline math with $ delimiters
function processMathInline(text: string): string {
  // Match inline math with $ delimiters
  const inlinePattern = /\$([^$]+)\$/g

  return text.replace(inlinePattern, (_match, math) => {
    return renderMath(math, false)
  })
}

// Process display math with $$ delimiters
function processMathDisplay(text: string): string {
  // Match display math with $$ delimiters - handle multiline
  const displayPattern = /\$\$([\s\S]*?)\$\$/g

  return text.replace(displayPattern, (_match, math) => {
    return `<div class="math-block">${renderMath(math.trim(), true)}</div>`
  })
}

// Don't override text renderer - it interferes with inline code tokenization
// Instead, we'll handle math in a preprocessing step

// Handle paragraph renderer to catch block-level math
const defaultParagraphOpen = md.renderer.rules.paragraph_open
const defaultParagraphClose = md.renderer.rules.paragraph_close

md.renderer.rules.paragraph_open = function (tokens, idx, options, env, renderer) {
  const nextToken = tokens[idx + 1]
  if (nextToken && nextToken.type === 'inline' && nextToken.content.trim().startsWith('$$')) {
    return '<div class="math-paragraph">'
  }
  if (defaultParagraphOpen) {
    return defaultParagraphOpen(tokens, idx, options, env, renderer)
  }
  return renderer.renderToken(tokens, idx, options)
}

md.renderer.rules.paragraph_close = function (tokens, idx, options, env, renderer) {
  const prevToken = tokens[idx - 1]
  if (prevToken && prevToken.type === 'inline' && prevToken.content.includes('$$')) {
    return '</div>'
  }
  if (defaultParagraphClose) {
    return defaultParagraphClose(tokens, idx, options, env, renderer)
  }
  return renderer.renderToken(tokens, idx, options)
}

// Parse frontmatter from markdown content
export function parseFrontmatter(content: string): { meta: MarkdownMeta; body: string } {
  const frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n/
  const match = content.match(frontmatterRegex)

  if (!match) {
    return { meta: {}, body: content }
  }

  const frontmatterText = match[1]
  const body = content.slice(match[0].length)

  // Parse YAML-like frontmatter
  const meta: MarkdownMeta = {}
  const lines = frontmatterText.split('\n')

  for (const line of lines) {
    const colonIndex = line.indexOf(':')
    if (colonIndex === -1) continue

    const key = line.slice(0, colonIndex).trim()
    const value = line.slice(colonIndex + 1).trim()

    // Handle different value types
    if (value.startsWith('[') && value.endsWith(']')) {
      // Array
      meta[key] = value
        .slice(1, -1)
        .split(',')
        .map(v => v.trim().replace(/^['"]|['"]$/g, ''))
    } else if (value === 'true' || value === 'false') {
      // Boolean
      meta[key] = value === 'true'
    } else if (!isNaN(Number(value))) {
      // Number
      meta[key] = Number(value)
    } else {
      // String (remove quotes if present)
      meta[key] = value.replace(/^['"]|['"]$/g, '')
    }
  }

  return { meta, body }
}

// Process markdown content
export function processMarkdown(content: string): ProcessedMarkdown {
  // Reset TOC items and heading tokens
  tocItems = []
  headingTokens = []
  

  // Parse frontmatter
  const { meta, body } = parseFrontmatter(content)

  // Pre-process math before markdown parsing to avoid conflicts with inline code
  let processedBody = body
  
  // Handle block-level math ($$...$$) that spans multiple lines
  processedBody = processedBody.replace(/^\$\$([\s\S]*?)\$\$/gm, (_match, math) => {
    // Wrap in a special div that won't be processed by markdown
    return `<div class="math-block">${renderMath(math.trim(), true)}</div>`
  })
  
  // Handle inline math ($...$) but be careful not to interfere with code blocks
  // Only process $ that are not inside backticks
  processedBody = processedBody.replace(/(?<!`)\$([^$\n]+)\$(?!`)/g, (_match, math) => {
    return renderMath(math, false)
  })

  // Render markdown to HTML
  const html = md.render(processedBody)
  
  // Build TOC tree from captured headings
  tocItems = buildTocTree(headingTokens)

  return {
    html,
    meta,
    toc: tocItems,
  }
}

// Load markdown content from file path
export async function loadMarkdownContent(path: string): Promise<string> {
  try {
    // In a real implementation, this would fetch from the server or use dynamic imports
    // For now, we'll use dynamic imports with Vite's glob import
    const modules = import.meta.glob('/src/content/**/*.md', {
      query: '?raw',
      import: 'default',
    })

    const modulePath = `/src/content/${path}.md`

    if (modules[modulePath]) {
      const content = (await modules[modulePath]()) as string
      return content
    }

    throw new Error(`Markdown file not found: ${path}`)
  } catch (error) {
    console.error('Error loading markdown content:', error)
    throw error
  }
}

// Process markdown file
export async function processMarkdownFile(path: string): Promise<ProcessedMarkdown> {
  const content = await loadMarkdownContent(path)
  return processMarkdown(content)
}
