import Prism from 'prismjs'

// Import core languages - ORDER MATTERS for dependencies!
// Basic languages first
import 'prismjs/components/prism-clike'  // Base for C-style languages
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-markup'  // HTML/XML base

// Languages with dependencies
import 'prismjs/components/prism-c'  // Must come before C++
import 'prismjs/components/prism-cpp'  // Depends on C
import 'prismjs/components/prism-typescript'  // Depends on JavaScript
import 'prismjs/components/prism-jsx'  // Depends on JavaScript and Markup
import 'prismjs/components/prism-tsx'  // Depends on TypeScript and JSX

// Independent languages
import 'prismjs/components/prism-json'
import 'prismjs/components/prism-markdown'
import 'prismjs/components/prism-python'
import 'prismjs/components/prism-bash'
import 'prismjs/components/prism-diff'
import 'prismjs/components/prism-sql'
import 'prismjs/components/prism-yaml'
import 'prismjs/components/prism-docker'

// Import theme
import 'prism-themes/themes/prism-one-dark.css'

function escapeHtml(text: string): string {
  const map: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  }
  
  return text.replace(/[&<>"']/g, m => map[m])
}

function getLanguageLabel(language: string): string {
  const labels: Record<string, string> = {
    javascript: 'JavaScript',
    typescript: 'TypeScript',
    jsx: 'JSX',
    tsx: 'TSX',
    css: 'CSS',
    scss: 'SCSS',
    html: 'HTML',
    xml: 'XML',
    json: 'JSON',
    yaml: 'YAML',
    markdown: 'Markdown',
    bash: 'Bash',
    shell: 'Shell',
    python: 'Python',
    java: 'Java',
    c: 'C',
    cpp: 'C++',
    csharp: 'C#',
    php: 'PHP',
    ruby: 'Ruby',
    go: 'Go',
    rust: 'Rust',
    sql: 'SQL',
    docker: 'Dockerfile',
    diff: 'Diff',
    git: 'Git',
    latex: 'LaTeX',
    plaintext: 'Plain Text'
  }
  
  return labels[language] || language.charAt(0).toUpperCase() + language.slice(1)
}

export function highlightCode(code: string, language: string): string {
  try {
    // Map common language aliases
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'sh': 'bash',
      'shell': 'bash',
      'yml': 'yaml',
      'md': 'markdown',
      'dockerfile': 'docker',
      'Dockerfile': 'docker',
      'html': 'markup',
      'xml': 'markup',
      'svg': 'markup',
    }
    
    const mappedLanguage = languageMap[language.toLowerCase()] || language.toLowerCase()
    
    // Check if language is supported
    let highlighted: string
    if (Prism.languages && Prism.languages[mappedLanguage]) {
      try {
        highlighted = Prism.highlight(
          code,
          Prism.languages[mappedLanguage],
          mappedLanguage
        )
      } catch (err) {
        console.warn(`Failed to highlight ${mappedLanguage}, using plain text`, err)
        highlighted = escapeHtml(code)
      }
    } else {
      // Fallback to escaped HTML
      highlighted = escapeHtml(code)
    }
    
    const languageLabel = getLanguageLabel(mappedLanguage)
    
    return `
      <div class="code-block-wrapper">
        <div class="code-block-header">
          <span class="code-block-language">${languageLabel}</span>
          <button class="code-block-copy" data-code="${escapeHtml(code).replace(/"/g, '&quot;')}">
            <svg class="copy-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" width="20" height="20">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            <span class="copy-text">Copy</span>
          </button>
        </div>
        <pre class="language-${mappedLanguage}"><code class="language-${mappedLanguage}">${highlighted}</code></pre>
      </div>
    `
  } catch (error) {
    console.error('Error highlighting code:', error)
    return `<pre><code>${escapeHtml(code)}</code></pre>`
  }
}

export function initCodeCopyButtons() {
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement
    const button = target.closest('.code-block-copy')
    
    if (button) {
      const codeAttr = button.getAttribute('data-code')
      if (codeAttr) {
        // Decode HTML entities
        const textarea = document.createElement('textarea')
        textarea.innerHTML = codeAttr
        const code = textarea.value
        
        navigator.clipboard.writeText(code).then(() => {
          const textEl = button.querySelector('.copy-text')
          if (textEl) {
            textEl.textContent = 'Copied!'
            setTimeout(() => {
              textEl.textContent = 'Copy'
            }, 2000)
          }
        }).catch(err => {
          console.error('Failed to copy code:', err)
        })
      }
    }
  })
}