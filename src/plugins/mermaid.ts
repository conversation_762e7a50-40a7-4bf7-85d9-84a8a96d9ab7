import mermaid from 'mermaid'

let isInitialized = false

export function initMermaid() {
  if (isInitialized) return
  
  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    themeVariables: {
      primaryColor: '#4F46E5',
      primaryTextColor: '#fff',
      primaryBorderColor: '#4338CA',
      lineColor: '#6366F1',
      secondaryColor: '#A78BFA',
      tertiaryColor: '#E0E7FF',
      background: '#FFFFFF',
      mainBkg: '#F3F4F6',
      secondBkg: '#E5E7EB',
      tertiaryBkg: '#D1D5DB'
    },
    flowchart: {
      htmlLabels: true,
      curve: 'basis',
      padding: 10
    },
    sequence: {
      diagramMarginX: 50,
      diagramMarginY: 10,
      actorMargin: 50,
      width: 150,
      height: 65,
      boxMargin: 10,
      boxTextMargin: 5,
      noteMargin: 10,
      messageMargin: 35,
      mirrorActors: true,
      bottomMarginAdj: 1,
      useMaxWidth: true
    },
    gantt: {
      numberSectionStyles: 4,
      fontSize: 11,
      gridLineStartPadding: 350,
      sectionFontSize: 16,
      leftPadding: 75
    }
  })
  
  isInitialized = true
}

export async function renderMermaidDiagram(code: string, id: string): Promise<string> {
  try {
    initMermaid()
    
    const { svg } = await mermaid.render(id, code)
    return `<div class="mermaid-container">${svg}</div>`
  } catch (error) {
    console.error('Error rendering Mermaid diagram:', error)
    return `<pre class="mermaid-error">Error rendering diagram: ${error instanceof Error ? error.message : 'Unknown error'}</pre>`
  }
}

export function renderMermaidSync(code: string): string {
  const id = `mermaid-${Math.random().toString(36).substr(2, 9)}`
  
  return `
    <div class="mermaid-wrapper" data-mermaid="${encodeURIComponent(code)}" data-id="${id}">
      <div class="mermaid-loading">Loading diagram...</div>
    </div>
  `
}

export function processMermaidDiagrams(element: HTMLElement) {
  const wrappers = element.querySelectorAll('.mermaid-wrapper')
  
  wrappers.forEach(async (wrapper) => {
    const code = decodeURIComponent(wrapper.getAttribute('data-mermaid') || '')
    const id = wrapper.getAttribute('data-id') || ''
    
    if (code && id) {
      const html = await renderMermaidDiagram(code, id)
      wrapper.innerHTML = html
    }
  })
}