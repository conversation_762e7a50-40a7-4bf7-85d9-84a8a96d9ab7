import { ref, Ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { MarkdownMeta, TocItem } from '@/services/markdownService'

export interface UseMarkdownContentOptions {
  // Base path for content files
  basePath?: string
  // Auto-load content based on route
  autoLoad?: boolean
  // Default meta if not provided in markdown
  defaultMeta?: Partial<MarkdownMeta>
}

export interface UseMarkdownContentReturn {
  content: Ref<string>
  html: Ref<string>
  meta: Ref<MarkdownMeta>
  toc: Ref<TocItem[]>
  loading: Ref<boolean>
  error: Ref<string | null>
  loadContent: (path: string) => Promise<void>
  processContent: (content: string) => Promise<void>
}

// Store for markdown modules
const markdownModules = import.meta.glob('/src/content/**/*.md', {
  query: '?raw',
  import: 'default',
})

export function useMarkdownContent(
  options: UseMarkdownContentOptions = {}
): UseMarkdownContentReturn {
  const { basePath = '', autoLoad = false, defaultMeta = {} } = options

  const route = useRoute()

  // State
  const content = ref('')
  const html = ref('')
  const meta = ref<MarkdownMeta>({ ...defaultMeta })
  const toc = ref<TocItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Process markdown content
  const processContent = async (markdownContent: string) => {
    try {
      loading.value = true
      error.value = null

      // Dynamically import the markdown service to avoid circular dependencies
      const { processMarkdown } = await import('@/services/markdownService')

      const processed = processMarkdown(markdownContent)

      content.value = markdownContent
      html.value = processed.html
      meta.value = { ...defaultMeta, ...processed.meta }
      toc.value = processed.toc
    } catch (err) {
      console.error('Error processing markdown:', err)
      error.value = err instanceof Error ? err.message : 'Failed to process markdown'
    } finally {
      loading.value = false
    }
  }

  // Load content from path
  const loadContent = async (path: string) => {
    try {
      loading.value = true
      error.value = null

      // Construct the full module path
      const fullPath = basePath ? `${basePath}/${path}` : path
      const modulePath = `/src/content/${fullPath}.md`

      if (!markdownModules[modulePath]) {
        throw new Error(`Markdown file not found: ${modulePath}`)
      }

      // Load the markdown content
      const markdownContent = (await markdownModules[modulePath]()) as string

      // Process the content
      await processContent(markdownContent)
    } catch (err) {
      console.error('Error loading markdown:', err)
      error.value = err instanceof Error ? err.message : 'Failed to load markdown'
    } finally {
      loading.value = false
    }
  }

  // Auto-load content based on route
  if (autoLoad) {
    watch(
      () => route.path,
      async path => {
        // Extract content path from route
        // Remove leading slash and base path if present
        let contentPath = path.replace(/^\//, '')

        // Remove team name from path if present
        const teamNamePattern = /^[^/]+\//
        if (teamNamePattern.test(contentPath)) {
          contentPath = contentPath.replace(teamNamePattern, '')
        }

        // Try to load the content
        if (contentPath) {
          await loadContent(contentPath)
        }
      },
      { immediate: true }
    )
  }

  return {
    content,
    html,
    meta,
    toc,
    loading,
    error,
    loadContent,
    processContent,
  }
}

// Helper to get all available markdown files
export async function getAvailableMarkdownFiles(): Promise<string[]> {
  const paths = Object.keys(markdownModules)
  return paths.map(path => {
    // Remove /src/content/ prefix and .md suffix
    return path.replace(/^\/src\/content\//, '').replace(/\.md$/, '')
  })
}

// Helper to preload markdown content
export async function preloadMarkdownContent(paths: string[]): Promise<void> {
  const promises = paths.map(path => {
    const modulePath = `/src/content/${path}.md`
    if (markdownModules[modulePath]) {
      return markdownModules[modulePath]()
    }
    return null
  })

  await Promise.all(promises.filter(Boolean))
}
