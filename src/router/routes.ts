/**
 * Unified route configuration
 * Single source of truth for route definitions
 */

import type { RouteConfig } from '@/types/router'
import type { RouteRecordRaw } from 'vue-router'

// Import all view components directly
import HomeView from '@/views/HomeView.vue'
import NotFound from '@/views/NotFound.vue'
import MarkdownPage from '@/views/MarkdownPage.vue'
import Attribution from '@/views/Attribution.vue'
import TeamMembers from '@/views/TeamMembers.vue'

// Get team information from environment variables
const TEAM_NAME = import.meta.env.VITE_TEAM_NAME || 'BASIS-China'
const TEAM_YEAR = import.meta.env.VITE_TEAM_YEAR || '2025'

// Create title generation function
export const createTitle = (pageName: string): string =>
  `${pageName} - ${TEAM_NAME} iGEM ${TEAM_YEAR}`

/**
 * Route configuration array
 * Contains all route definitions for router and search services
 */
export const routeConfigs: RouteConfig[] = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
    meta: {
      title: 'SnaPFAS - BASIS CHINA iGEM 2025',
      category: 'home',
    },
  },
  {
    path: '/teammembers',
    name: 'team-members',
    component: TeamMembers,
    meta: {
      title: 'Team Members',
      category: 'team',
    },
  },
  {
    path: '/attribution',
    name: 'attribution',
    component: Attribution,
    meta: {
      title: 'Attribution',
      category: 'team',
    },
  },
  {
    path: '/human-practices',
    name: 'human-practices',
    component: MarkdownPage,
    meta: {
      title: 'Human Practices',
      category: 'human-practices',
    },
  },
  {
    path: '/project/overview',
    name: 'project-overview',
    component: MarkdownPage,
    meta: {
      title: 'Project Overview',
      category: 'project',
    },
  },
  {
    path: '/project/design',
    name: 'project-design',
    component: MarkdownPage,
    meta: {
      title: 'Project Design',
      category: 'project',
    },
  },
  // Wet Lab Routes
  {
    path: '/wet-lab/experiment',
    name: 'wet-lab-experiment',
    component: MarkdownPage,
    meta: {
      title: 'Wet Lab Experiment',
      category: 'wet-lab',
    },
  },
  {
    path: '/wet-lab/parts',
    name: 'wet-lab-parts',
    component: MarkdownPage,
    meta: {
      title: 'Wet Lab Parts',
      category: 'wet-lab',
    },
  },
  {
    path: '/wet-lab/results',
    name: 'wet-lab-results',
    component: MarkdownPage,
    meta: {
      title: 'Wet Lab Results',
      category: 'wet-lab',
    },
  },
  {
    path: '/wet-lab/safety',
    name: 'wet-lab-safety',
    component: MarkdownPage,
    meta: {
      title: 'Wet Lab Safety',
      category: 'wet-lab',
    },
  },
  {
    path: '/wet-lab/engineering',
    name: 'wet-lab-engineering',
    component: MarkdownPage,
    meta: {
      title: 'Wet Lab Engineering',
      category: 'wet-lab',
    },
  },
  // Dry Lab Routes
  {
    path: '/dry-lab/model',
    name: 'dry-lab-model',
    component: MarkdownPage,
    meta: {
      title: 'Dry Lab Model',
      category: 'dry-lab',
    },
  },
  {
    path: '/dry-lab/hardware',
    name: 'dry-lab-hardware',
    component: MarkdownPage,
    meta: {
      title: 'Dry Lab Hardware',
      category: 'dry-lab',
    },
  },
  {
    path: '/dry-lab/software',
    name: 'dry-lab-software',
    component: MarkdownPage,
    meta: {
      title: 'Dry Lab Software',
      category: 'dry-lab',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: NotFound,
    meta: {
      title: 'Page Not Found',
      category: 'other',
    },
  },
]

/**
 * Convert route configuration to Vue Router format
 * Add complete titles to each route
 */
export const routes: RouteRecordRaw[] = routeConfigs.map(route => {
  const routeRecord: RouteRecordRaw = {
    path: route.path,
    name: route.name,
    component: route.component,
    meta: {
      ...route.meta,
      title: route.name === 'home' ? route.meta?.title : createTitle(route.meta?.title || ''),
    },
  }

  // Add children if they exist
  if (route.children && route.children.length > 0) {
    ;(routeRecord as any).children = route.children.map(child => ({
      path: child.path,
      name: child.name,
      component: child.component,
      meta: {
        ...child.meta,
        title: child.name === 'home' ? child.meta?.title : createTitle(child.meta?.title || ''),
      },
    }))
  }

  // Add redirect if it exists
  if (route.redirect) {
    ;(routeRecord as any).redirect = route.redirect
  }

  return routeRecord
})
