<template>
  <div class="overflow-hidden">
    <!-- Modern Hero Section with Bento Grid and 3D Elements -->
    <section
      ref="heroSection"
      class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-primary-50/20 overflow-hidden"
    >
      <!-- 3D Animated Background Elements -->
      <div class="absolute inset-0 overflow-hidden">
        <!-- Floating 3D orbs with gradient -->
        <div
          class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary-400/20 to-secondary-400/20 rounded-full blur-3xl animate-float"
        ></div>
        <div
          class="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-br from-secondary-400/20 to-primary-400/20 rounded-full blur-3xl animate-float-delayed"
        ></div>
        <div
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[40rem] h-[40rem] bg-gradient-to-br from-primary-300/10 to-secondary-300/10 rounded-full blur-3xl"
        ></div>

        <!-- Grid pattern overlay -->
        <div class="absolute inset-0 bg-grid-pattern opacity-[0.02]"></div>
      </div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
        <!-- Main Hero Content with Bold Typography -->
        <div class="text-center mb-16" data-aos="fade-up">
          <!-- Modern Badge -->
          <Badge variant="success" class="mb-6 inline-flex">
            <Sparkles class="w-3 h-3 mr-1" />
            iGEM 2025 Competition
          </Badge>

          <!-- Bold Hero Title with Gradient -->
          <h1 class="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black mb-6 tracking-tight">
            <span class="block text-slate-900">Breaking Down The</span>
            <span
              class="block text-transparent bg-clip-text bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-600 animate-gradient"
            >
              Unbreakable
            </span>
          </h1>

          <!-- Minimal Subtitle -->
          <p
            class="max-w-3xl mx-auto text-xl sm:text-2xl text-slate-600 mb-12 font-light leading-relaxed"
          >
            Innovative biological solutions powered by synthetic biology to remediate PFAS
            contamination
          </p>

          <!-- Modern CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              as="router-link"
              to="/project/overview"
              size="xl"
              variant="default"
              class="group"
            >
              <span>Discover Our Solution</span>
              <ArrowRight class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>

            <Button as="a" href="#features" size="xl" variant="outline" class="group">
              <span>See How It Works</span>
              <ChevronDown class="w-5 h-5 ml-2 group-hover:translate-y-1 transition-transform" />
            </Button>
          </div>
        </div>

        <!-- Hero Stats - More Integrated Design -->
        <div
          class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-16 max-w-4xl mx-auto"
          data-aos="fade-up"
          data-aos-delay="200"
        >
          <div
            class="bg-white/50 backdrop-blur-sm border border-slate-200/30 rounded-2xl p-6 text-center hover:bg-white/60 transition-all duration-300 group"
          >
            <div
              class="w-12 h-12 bg-primary-100/50 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:bg-primary-100/70 transition-colors"
            >
              <Beaker class="w-6 h-6 text-primary-600" />
            </div>
            <h3 class="text-2xl font-bold text-slate-800 mb-1">
              <AnimatedNumber :value="4000" suffix="+" :duration="2500" />
            </h3>
            <p class="text-sm text-slate-600">PFAS Compounds</p>
          </div>

          <div
            class="bg-white/50 backdrop-blur-sm border border-slate-200/30 rounded-2xl p-6 text-center hover:bg-white/60 transition-all duration-300 group"
          >
            <div
              class="w-12 h-12 bg-secondary-100/50 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:bg-secondary-100/70 transition-colors"
            >
              <Users class="w-6 h-6 text-secondary-600" />
            </div>
            <h3 class="text-2xl font-bold text-slate-800 mb-1">
              <AnimatedNumber :value="97" suffix="%" :duration="2000" />
            </h3>
            <p class="text-sm text-slate-600">Population Affected</p>
          </div>

          <div
            class="bg-white/50 backdrop-blur-sm border border-slate-200/30 rounded-2xl p-6 text-center hover:bg-white/60 transition-all duration-300 group"
          >
            <div
              class="w-12 h-12 bg-green-100/50 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:bg-green-100/70 transition-colors"
            >
              <Leaf class="w-6 h-6 text-green-600" />
            </div>
            <h3 class="text-2xl font-bold text-slate-800 mb-1">
              <AnimatedNumber :value="100" suffix="%" :duration="2300" />
            </h3>
            <p class="text-sm text-slate-600">Biological Solution</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section with Modern Bento Grid -->
    <section id="features" class="py-24 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16" data-aos="fade-up">
          <Badge variant="outline" class="mb-4">
            <Microscope class="w-3 h-3 mr-1" />
            The Science
          </Badge>
          <h2 class="text-4xl sm:text-5xl font-bold text-slate-900 mb-4">Understanding PFAS</h2>
          <p class="text-xl text-slate-600 max-w-3xl mx-auto">
            Per- and polyfluoroalkyl substances (PFAS) are synthetic chemicals that persist in the
            environment
          </p>
        </div>

        <!-- Bento Grid Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="100">
          <!-- Large Feature Card -->
          <Card
            class="lg:col-span-2 lg:row-span-2 bg-gradient-to-br from-primary-50 to-white border-primary-100 overflow-hidden group"
          >
            <CardContent class="p-8">
              <div>
                <div
                  class="w-14 h-14 bg-white rounded-2xl flex items-center justify-center shadow-lg mb-4"
                >
                  <FlaskRound class="w-7 h-7 text-primary-600" />
                </div>
                <h3 class="text-2xl font-bold text-slate-900 mb-3">What are PFAS?</h3>
                <p class="text-slate-600 leading-relaxed">
                  PFAS are a group of manufactured chemicals used in industry worldwide since the
                  1940s. They're found in non-stick cookware, water-resistant clothing, food
                  packaging, and firefighting foam. Their carbon-fluorine bonds make them incredibly
                  resistant to breakdown.
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- Medium Feature Cards -->
          <Card
            class="lg:col-span-2 bg-gradient-to-br from-secondary-50 to-white border-secondary-100 overflow-hidden"
          >
            <CardContent class="p-6">
              <div class="flex items-start space-x-4">
                <div
                  class="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-md flex-shrink-0"
                >
                  <Globe class="w-6 h-6 text-secondary-600" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Global Impact</h3>
                  <p class="text-sm text-slate-600">
                    PFAS contamination affects water sources, soil, and air quality worldwide, with
                    detection even in remote Arctic regions.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card class="bg-gradient-to-br from-red-50 to-white border-red-100 overflow-hidden">
            <CardContent class="p-6">
              <div class="flex items-start space-x-4">
                <div
                  class="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-md flex-shrink-0"
                >
                  <AlertTriangle class="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Health Risks</h3>
                  <p class="text-sm text-slate-600">
                    Linked to cancer, liver damage, decreased fertility, and increased cholesterol.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card class="bg-gradient-to-br from-green-50 to-white border-green-100 overflow-hidden">
            <CardContent class="p-6">
              <div class="flex items-start space-x-4">
                <div
                  class="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-md flex-shrink-0"
                >
                  <Leaf class="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-slate-900 mb-2">Our Solution</h3>
                  <p class="text-sm text-slate-600">
                    Engineered bacteria that can break down PFAS molecules naturally and safely.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Enhanced Stats Grid with Data Stories -->
        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12"
          data-aos="fade-up"
          data-aos-delay="200"
        >
          <!-- PFAS Variants Card -->
          <div class="group relative overflow-hidden">
            <Card
              class="h-full bg-gradient-to-br from-orange-50 via-white to-orange-50 border-orange-200 hover:border-orange-300 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2"
            >
              <CardContent class="p-6">
                <div class="flex items-start justify-between mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
                  >
                    <FlaskRound class="w-6 h-6 text-orange-600" />
                  </div>
                  <div
                    class="text-xs text-orange-600 font-medium bg-orange-100 px-2 py-1 rounded-full"
                  >
                    Chemical Diversity
                  </div>
                </div>

                <div class="mb-3">
                  <div class="text-3xl font-black text-orange-700 mb-1 tracking-tight">
                    <AnimatedNumber :value="15000" suffix="+" :duration="2500" separator="," />
                  </div>
                  <div class="text-sm font-semibold text-orange-600 mb-2">PFAS Chemicals</div>
                  <div class="text-xs text-slate-600 leading-relaxed">
                    Identified in EPA database
                  </div>
                </div>

                <!-- Progress bar showing scale -->
                <div class="mb-3">
                  <div class="flex justify-between text-xs text-slate-500 mb-1">
                    <span>Known variants</span>
                    <span>Growing daily</span>
                  </div>
                  <div class="w-full bg-orange-100 rounded-full h-2">
                    <div
                      class="bg-gradient-to-r from-orange-400 to-orange-600 h-2 rounded-full animate-pulse"
                      style="width: 85%"
                    ></div>
                  </div>
                </div>

                <div class="flex items-center text-orange-600 text-xs font-medium">
                  <TrendingUp class="w-3 h-3 mr-1" />
                  <span>200+ new annually</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Degradation Time Card -->
          <div class="group relative overflow-hidden">
            <Card
              class="h-full bg-gradient-to-br from-red-50 via-white to-red-50 border-red-200 hover:border-red-300 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2"
            >
              <CardContent class="p-6">
                <div class="flex items-start justify-between mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
                  >
                    <Clock class="w-6 h-6 text-red-600" />
                  </div>
                  <div class="text-xs text-red-600 font-medium bg-red-100 px-2 py-1 rounded-full">
                    Persistence
                  </div>
                </div>

                <div class="mb-3">
                  <div class="text-3xl font-black text-red-700 mb-1 tracking-tight">
                    <AnimatedNumber :value="1000" suffix="+" :duration="2000" separator="," />
                  </div>
                  <div class="text-sm font-semibold text-red-600 mb-2">Years to Degrade</div>
                  <div class="text-xs text-slate-600 leading-relaxed">Traditional methods</div>
                </div>

                <!-- Comparison visualization -->
                <div class="mb-3">
                  <div class="text-xs text-slate-500 mb-1">vs. Our Solution: 30 days</div>
                  <div class="space-y-1">
                    <div class="w-full bg-red-200 rounded-full h-1.5">
                      <div class="bg-red-500 h-1.5 rounded-full" style="width: 100%"></div>
                    </div>
                    <div class="w-full bg-green-100 rounded-full h-1.5">
                      <div
                        class="bg-green-500 h-1.5 rounded-full animate-pulse"
                        style="width: 3%"
                      ></div>
                    </div>
                  </div>
                </div>

                <div class="flex items-center text-red-600 text-xs font-medium">
                  <AlertTriangle class="w-3 h-3 mr-1" />
                  <span>Forever chemicals</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Population Impact Card -->
          <div class="group relative overflow-hidden">
            <Card
              class="h-full bg-gradient-to-br from-purple-50 via-white to-purple-50 border-purple-200 hover:border-purple-300 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2"
            >
              <CardContent class="p-6">
                <div class="flex items-start justify-between mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
                  >
                    <Users class="w-6 h-6 text-purple-600" />
                  </div>
                  <div
                    class="text-xs text-purple-600 font-medium bg-purple-100 px-2 py-1 rounded-full"
                  >
                    Global Crisis
                  </div>
                </div>

                <div class="mb-3">
                  <div class="text-3xl font-black text-purple-700 mb-1 tracking-tight">
                    <AnimatedNumber :value="97" suffix="%" :duration="2000" />
                  </div>
                  <div class="text-sm font-semibold text-purple-600 mb-2">Population Exposed</div>
                  <div class="text-xs text-slate-600 leading-relaxed">Worldwide contamination</div>
                </div>

                <!-- Global impact visualization -->
                <div class="mb-3">
                  <div class="text-xs text-slate-500 mb-1">Affected regions</div>
                  <div class="grid grid-cols-4 gap-1">
                    <div class="h-2 bg-purple-400 rounded-sm animate-pulse"></div>
                    <div
                      class="h-2 bg-purple-400 rounded-sm animate-pulse"
                      style="animation-delay: 0.1s"
                    ></div>
                    <div
                      class="h-2 bg-purple-400 rounded-sm animate-pulse"
                      style="animation-delay: 0.2s"
                    ></div>
                    <div
                      class="h-2 bg-purple-300 rounded-sm animate-pulse"
                      style="animation-delay: 0.3s"
                    ></div>
                  </div>
                </div>

                <div class="flex items-center text-purple-600 text-xs font-medium">
                  <Globe class="w-3 h-3 mr-1" />
                  <span>Including Arctic regions</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Our Solution Card -->
          <div class="group relative overflow-hidden">
            <Card
              class="h-full bg-gradient-to-br from-green-50 via-white to-green-50 border-green-200 hover:border-green-300 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2"
            >
              <CardContent class="p-6">
                <div class="flex items-start justify-between mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
                  >
                    <Leaf class="w-6 h-6 text-green-600" />
                  </div>
                  <div
                    class="text-xs text-green-600 font-medium bg-green-100 px-2 py-1 rounded-full"
                  >
                    Innovation
                  </div>
                </div>

                <div class="mb-3">
                  <div class="text-3xl font-black text-green-700 mb-1 tracking-tight">
                    <AnimatedNumber :value="100" suffix="%" :duration="2300" />
                  </div>
                  <div class="text-sm font-semibold text-green-600 mb-2">Biological Solution</div>
                  <div class="text-xs text-slate-600 leading-relaxed">
                    Natural breakdown process
                  </div>
                </div>

                <!-- Success indicator -->
                <div class="mb-3">
                  <div class="text-xs text-slate-500 mb-1">Breakthrough efficiency</div>
                  <div class="w-full bg-green-100 rounded-full h-2">
                    <div
                      class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full animate-pulse"
                      style="width: 100%"
                    ></div>
                  </div>
                </div>

                <div class="flex items-center text-green-600 text-xs font-medium">
                  <Sparkles class="w-3 h-3 mr-1" />
                  <span>Engineered bacteria</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <!-- Market Impact Section -->
        <div class="mt-16" data-aos="fade-up" data-aos-delay="300">
          <div class="text-center mb-8">
            <Badge variant="secondary" class="mb-4">
              <TrendingUp class="w-3 h-3 mr-1" />
              Market Impact
            </Badge>
            <h3 class="text-2xl sm:text-3xl font-bold text-slate-900 mb-2">
              Economic & Environmental Value
            </h3>
            <p class="text-slate-600 max-w-2xl mx-auto">
              Our breakthrough solution addresses a multi-billion dollar market while delivering
              measurable environmental benefits
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Market Size Card -->
            <Card
              class="bg-gradient-to-br from-blue-50 via-white to-blue-50 border-blue-200 hover:border-blue-300 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 text-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-4"
                >
                  <TrendingUp class="w-8 h-8 text-blue-600" />
                </div>
                <div class="text-4xl font-black text-blue-700 mb-2">
                  <AnimatedNumber :value="28" suffix="B+" :duration="2000" prefix="$" />
                </div>
                <div class="text-sm font-semibold text-blue-600 mb-2">Global PFAS Market</div>
                <div class="text-xs text-slate-600">Annual market size (2023)</div>
                <div class="mt-3 text-xs text-blue-600 font-medium">Growing 8.5% annually</div>
              </CardContent>
            </Card>

            <!-- Remediation Cost Card -->
            <Card
              class="bg-gradient-to-br from-amber-50 via-white to-amber-50 border-amber-200 hover:border-amber-300 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 text-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-amber-100 to-amber-200 rounded-2xl flex items-center justify-center mx-auto mb-4"
                >
                  <AlertTriangle class="w-8 h-8 text-amber-600" />
                </div>
                <div class="text-4xl font-black text-amber-700 mb-2">
                  <AnimatedNumber :value="400" suffix="B+" :duration="2200" prefix="$" />
                </div>
                <div class="text-sm font-semibold text-amber-600 mb-2">Cleanup Costs</div>
                <div class="text-xs text-slate-600">Estimated global remediation</div>
                <div class="mt-3 text-xs text-amber-600 font-medium">Traditional methods</div>
              </CardContent>
            </Card>

            <!-- Our Solution Value Card -->
            <Card
              class="bg-gradient-to-br from-emerald-50 via-white to-emerald-50 border-emerald-200 hover:border-emerald-300 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 text-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-2xl flex items-center justify-center mx-auto mb-4"
                >
                  <Sparkles class="w-8 h-8 text-emerald-600" />
                </div>
                <div class="text-4xl font-black text-emerald-700 mb-2">
                  <AnimatedNumber :value="90" suffix="%" :duration="2400" />
                </div>
                <div class="text-sm font-semibold text-emerald-600 mb-2">Cost Reduction</div>
                <div class="text-xs text-slate-600">Compared to current methods</div>
                <div class="mt-3 text-xs text-emerald-600 font-medium">Biological efficiency</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>

    <!-- Navigation Section with Modern Cards -->
    <section class="py-24 bg-gradient-to-b from-white to-slate-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
          <Badge variant="secondary" class="mb-4">
            <Compass class="w-3 h-3 mr-1" />
            Explore
          </Badge>
          <h2 class="text-4xl sm:text-5xl font-bold text-slate-900 mb-4">
            Dive Deeper Into Our Project
          </h2>
          <p class="text-xl text-slate-600 max-w-3xl mx-auto">
            Explore the different aspects of our iGEM journey and synthetic biology solution
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Project Overview Card -->
          <router-link
            to="/project/overview"
            class="group relative"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <Card
              class="h-full bg-white hover:bg-gradient-to-br hover:from-primary-50 hover:to-white border-2 border-slate-200 hover:border-primary-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 relative z-10">
                <div class="mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform"
                  >
                    <FileText class="w-6 h-6 text-primary-600" />
                  </div>
                </div>
                <h3
                  class="text-xl font-semibold text-slate-900 mb-2 group-hover:text-primary-700 transition-colors"
                >
                  Project Overview
                </h3>
                <p class="text-slate-600 text-sm mb-4">
                  Discover our innovative approach to breaking down PFAS using engineered bacteria
                </p>
                <div class="flex items-center text-primary-600 text-sm font-medium">
                  <span>Learn more</span>
                  <ArrowRight class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </CardContent>
            </Card>
          </router-link>

          <!-- Human Practices Card -->
          <router-link
            to="/human-practices"
            class="group relative"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <Card
              class="h-full bg-white hover:bg-gradient-to-br hover:from-secondary-50 hover:to-white border-2 border-slate-200 hover:border-secondary-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 relative z-10">
                <div class="mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform"
                  >
                    <Users class="w-6 h-6 text-secondary-600" />
                  </div>
                </div>
                <h3
                  class="text-xl font-semibold text-slate-900 mb-2 group-hover:text-secondary-700 transition-colors"
                >
                  Human Practices
                </h3>
                <p class="text-slate-600 text-sm mb-4">
                  Engaging with communities and stakeholders to create real-world impact
                </p>
                <div class="flex items-center text-secondary-600 text-sm font-medium">
                  <span>Explore impact</span>
                  <ArrowRight class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </CardContent>
            </Card>
          </router-link>

          <!-- Team Card -->
          <router-link
            to="/teammembers"
            class="group relative"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            <Card
              class="h-full bg-white hover:bg-gradient-to-br hover:from-green-50 hover:to-white border-2 border-slate-200 hover:border-green-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 relative z-10">
                <div class="mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform"
                  >
                    <UserCheck class="w-6 h-6 text-green-600" />
                  </div>
                </div>
                <h3
                  class="text-xl font-semibold text-slate-900 mb-2 group-hover:text-green-700 transition-colors"
                >
                  Our Team
                </h3>
                <p class="text-slate-600 text-sm mb-4">
                  Meet the brilliant minds behind the SnaPFAS project
                </p>
                <div class="flex items-center text-green-600 text-sm font-medium">
                  <span>Meet the team</span>
                  <ArrowRight class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </CardContent>
            </Card>
          </router-link>

          <!-- Attribution Card -->
          <router-link
            to="/attribution"
            class="group relative"
            data-aos="fade-up"
            data-aos-delay="400"
          >
            <Card
              class="h-full bg-white hover:bg-gradient-to-br hover:from-purple-50 hover:to-white border-2 border-slate-200 hover:border-purple-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <CardContent class="p-6 relative z-10">
                <div class="mb-4">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform"
                  >
                    <Award class="w-6 h-6 text-purple-600" />
                  </div>
                </div>
                <h3
                  class="text-xl font-semibold text-slate-900 mb-2 group-hover:text-purple-700 transition-colors"
                >
                  Attribution
                </h3>
                <p class="text-slate-600 text-sm mb-4">
                  Acknowledging contributions and support from our community
                </p>
                <div class="flex items-center text-purple-600 text-sm font-medium">
                  <span>View credits</span>
                  <ArrowRight class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </CardContent>
            </Card>
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup name="HomeView">
import AnimatedNumber from '@/components/ui/AnimatedNumber.vue'
import Badge from '@/components/ui/Badge.vue'
import Button from '@/components/ui/Button.vue'
import Card from '@/components/ui/Card.vue'
import CardContent from '@/components/ui/CardContent.vue'
import AOS from 'aos'
import 'aos/dist/aos.css'
import {
  AlertTriangle,
  ArrowRight,
  Award,
  Beaker,
  ChevronDown,
  Clock,
  Compass,
  FileText,
  FlaskRound,
  Globe,
  Leaf,
  Microscope,
  Sparkles,
  TrendingUp,
  UserCheck,
  Users,
} from 'lucide-vue-next'
import { onMounted, ref } from 'vue'

const heroSection = ref(null)

onMounted(() => {
  AOS.init({
    duration: 1000,
    easing: 'ease-out-cubic',
    once: true,
    offset: 50,
    delay: 50,
  })
})
</script>

<style scoped>
/* Grid pattern background */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* Modern gradient animations */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  animation: gradient 15s ease infinite;
  background-size: 400% 400%;
}

/* Floating animation for 3D orbs */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 8s ease-in-out 2s infinite;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility */
a:focus-visible,
button:focus-visible {
  outline: 2px solid var(--color-primary-400);
  outline-offset: 2px;
}

/* Prevent horizontal overflow */
section {
  overflow-x: hidden;
}

/* Responsive hero section */
@media (max-width: 768px) {
  .min-h-screen {
    min-height: 100vh;
    padding-top: 4rem;
  }
}

/* Card hover effects */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\:translate-x-1 {
  transform: translateX(0.25rem);
}

.group:hover .group-hover\:translate-y-1 {
  transform: translateY(0.25rem);
}

/* Gradient border effect for cards */
.gradient-border {
  position: relative;
  background:
    linear-gradient(white, white) padding-box,
    linear-gradient(to right, var(--color-primary-600), var(--color-secondary-600)) border-box;
  border: 1px solid transparent;
}

/* AOS custom animations */
[data-aos='fade-up'] {
  transform: translateY(30px);
  opacity: 0;
  transition-property: transform, opacity;
  &.aos-animate {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary-400);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-600);
}
</style>
