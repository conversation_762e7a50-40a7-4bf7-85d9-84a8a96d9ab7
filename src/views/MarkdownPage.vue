<template>
  <MarkdownArticle 
    :content-path="contentPath"
    :show-toc="true"
    toc-title="Contents"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import MarkdownArticle from '@/components/MarkdownArticle.vue'

const route = useRoute()

// Map routes to content paths
const routeContentMap: Record<string, string> = {
  '/human-practices': 'articles/human-practices',
  '/project/overview': 'articles/project-overview',
  '/project/design': 'articles/project-design',
  '/wet-lab/experiment': 'articles/wet-lab/experiment',
  '/wet-lab/engineering': 'articles/wet-lab/engineering',
  '/wet-lab/parts': 'articles/wet-lab/parts',
  '/wet-lab/results': 'articles/wet-lab/results',
  '/wet-lab/safety': 'articles/wet-lab/safety',
  '/dry-lab/model': 'articles/dry-lab/model',
  '/dry-lab/hardware': 'articles/dry-lab/hardware',
  '/dry-lab/software': 'articles/dry-lab/software',
}

// Get content path based on current route
const contentPath = computed(() => {
  // Remove base path (team name) from route
  const path = route.path.replace(/^\/[^/]+/, '')
  return routeContentMap[path] || routeContentMap[route.path] || ''
})
</script>