---
title: Hardware Development
description: Design and construction of hardware components for our biosensor system.
author: BASIS-China Team
date: 2025-01-14
tags: [hardware, device, instrumentation, engineering]
---

## Overview

We developed custom hardware to support field deployment of our biosensor, including a portable detection device and automated sampling system.

## Portable Detection Device

### Design Requirements

- **Portability**: <2 kg total weight
- **Battery life**: >8 hours continuous operation
- **Sensitivity**: Detect fluorescence at 10 nM
- **User interface**: Simple, intuitive operation
- **Cost**: <$500 per unit

### System Architecture

```
┌─────────────┐     ┌──────────────┐     ┌───────────┐
│   Sample    │────▶│   Optical    │────▶│   Data    │
│   Chamber   │     │   Module     │     │   System  │
└─────────────┘     └──────────────┘     └───────────┘
        │                   │                    │
        └───────────────────┴────────────────────┘
                            │
                    ┌───────────────┐
                    │   Controller  │
                    └───────────────┘
```

### Optical System

#### Excitation Module
- LED: 488 nm, 100 mW
- Excitation filter: 480/20 nm bandpass
- Collimating lens: f = 10 mm

#### Detection Module
- Photodiode: Hamamatsu S1337
- Emission filter: 520/20 nm bandpass
- Gain: Adjustable 10¹ - 10⁶

#### Signal Processing

Signal-to-noise ratio:

$$SNR = \frac{P_{signal}}{\sqrt{P_{noise}^2 + P_{dark}^2 + P_{shot}^2}}$$

Achieved SNR > 100 at 10 nM concentration.

## Microfluidic Chip

### Design Features

- **Material**: PDMS on glass
- **Channel width**: 100 μm
- **Sample volume**: 10 μL
- **Flow rate**: 10-100 μL/min

### Fabrication Process

1. **Master mold creation**
   - SU-8 photolithography
   - Feature height: 50 μm
   - Resolution: 10 μm

2. **PDMS casting**
   - Mix ratio 10:1 (base:curing agent)
   - Cure at 70°C for 2 hours
   - Plasma bonding to glass

### Flow Dynamics

Reynolds number for microfluidic flow:

$$Re = \frac{\rho v D}{\mu} < 1$$

Ensuring laminar flow throughout the device.

## Electronics Design

### Microcontroller System

**Specifications**:
- MCU: STM32F4 ARM Cortex-M4
- Clock: 168 MHz
- ADC: 12-bit, 2.4 MSPS
- Communication: USB, Bluetooth 4.0

### Power Management

Power consumption breakdown:

| Component | Power (mW) | Percentage |
|-----------|------------|------------|
| LED | 100 | 40% |
| MCU | 50 | 20% |
| Display | 75 | 30% |
| Others | 25 | 10% |

Battery capacity required:

$$Capacity = \frac{P_{total} \times t_{operation}}{V_{battery} \times \eta}$$

Using 3.7V, 5000mAh Li-Po battery provides >10 hours operation.

## Automated Sampler

### Mechanical Design

- **Sampling rate**: 1 sample/hour
- **Sample storage**: 24 vials
- **Temperature control**: 4-37°C ± 0.5°C
- **Contamination prevention**: Automated washing

### Control Algorithm

```python
def sampling_sequence():
    move_to_position(sample_location)
    lower_probe()
    draw_sample(volume=100)  # μL
    lift_probe()
    move_to_vial(current_vial)
    dispense_sample()
    wash_probe()
    current_vial += 1
```

## Data Acquisition System

### Signal Processing

Digital filtering using Butterworth filter:

$$H(z) = \frac{b_0 + b_1z^{-1} + b_2z^{-2}}{1 + a_1z^{-1} + a_2z^{-2}}$$

### Data Storage

- Local storage: 32 GB SD card
- Data format: CSV with timestamps
- Backup: Cloud sync when connected

### Wireless Communication

- Protocol: MQTT over WiFi/4G
- Encryption: AES-256
- Update rate: Real-time or batch

## User Interface

### Display Features

- 3.5" touchscreen LCD
- Resolution: 480×320 pixels
- Real-time concentration plot
- Battery and status indicators

### Mobile App

Features:
- Remote monitoring
- Data visualization
- Alert notifications
- Historical data access

## Calibration System

### Automated Calibration

$$C_{actual} = \frac{S - S_0}{m}$$

Where:
- $S$ = Measured signal
- $S_0$ = Baseline
- $m$ = Calibration slope

### Quality Control

Built-in checks:
- Positive control: Known concentration
- Negative control: Blank sample
- Internal standard: Spike recovery

## Cost Analysis

### Bill of Materials

| Component | Cost ($) | Quantity | Total ($) |
|-----------|----------|----------|-----------|
| MCU Board | 50 | 1 | 50 |
| Optical Components | 150 | 1 | 150 |
| Enclosure | 30 | 1 | 30 |
| Display | 40 | 1 | 40 |
| Battery | 20 | 1 | 20 |
| Microfluidics | 50 | 1 | 50 |
| Others | 60 | 1 | 60 |
| **Total** | | | **400** |

### Manufacturing Scale

Cost reduction at scale:
- 10 units: $400/unit
- 100 units: $250/unit
- 1000 units: $150/unit

## Future Improvements

1. **Miniaturization**: Reduce to smartphone size
2. **Multiplexing**: Detect multiple targets
3. **AI Integration**: Pattern recognition
4. **Energy Harvesting**: Solar power option
5. **IoT Integration**: Full sensor network deployment