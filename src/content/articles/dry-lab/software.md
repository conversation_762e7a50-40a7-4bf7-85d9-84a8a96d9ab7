---
title: Software Development
description: Software tools and applications developed for data analysis and system control.
author: BASIS-China Team
date: 2025-01-14
tags: [software, programming, tools, analysis]
---

## Overview

We developed comprehensive software solutions for data acquisition, analysis, and visualization to support our biosensor project.

## Data Analysis Pipeline

### Architecture

```mermaid
graph LR
    A[Raw Data] --> B[Preprocessing]
    B --> C[Feature Extraction]
    C --> D[Statistical Analysis]
    D --> E[Visualization]
    E --> F[Report Generation]
```

### Implementation

Core analysis functions in Python:

```python
import numpy as np
import pandas as pd
from scipy import stats

class BiosensorAnalyzer:
    def __init__(self, data_path):
        self.data = pd.read_csv(data_path)
        self.baseline = None
        self.calibration = None
    
    def preprocess(self):
        # Remove outliers using IQR method
        Q1 = self.data.quantile(0.25)
        Q3 = self.data.quantile(0.75)
        IQR = Q3 - Q1
        self.data = self.data[~((self.data < (Q1 - 1.5 * IQR)) | 
                                 (self.data > (Q3 + 1.5 * IQR)))]
        
    def fit_dose_response(self, x, y):
        # Hill equation fitting
        from scipy.optimize import curve_fit
        
        def hill_equation(x, bottom, top, kd, n):
            return bottom + (top - bottom) * (x**n / (kd**n + x**n))
        
        popt, _ = curve_fit(hill_equation, x, y)
        return popt
```

## Real-Time Control System

### Firmware Architecture

Embedded C++ for microcontroller:

```cpp
class SensorController {
private:
    float temperature;
    float fluorescence;
    uint32_t timestamp;
    
public:
    void initialize() {
        // Setup ADC, timers, and communication
        ADC_Init();
        Timer_Init(SAMPLE_RATE);
        UART_Init(115200);
    }
    
    float readFluorescence() {
        uint16_t raw = ADC_Read(CHANNEL_FLUOR);
        return calibrate(raw);
    }
    
    void controlLoop() {
        while(true) {
            fluorescence = readFluorescence();
            temperature = readTemperature();
            
            if (fluorescence > THRESHOLD) {
                triggerAlert();
            }
            
            logData(timestamp, fluorescence, temperature);
            delay(SAMPLE_INTERVAL);
        }
    }
};
```

### Communication Protocol

Custom protocol for device communication:

```
Frame Structure:
[START][LENGTH][CMD][DATA][CRC][END]
  0xAA   2bytes  1byte  Nbytes 2bytes 0x55
```

## Web Dashboard

### Technology Stack

- **Frontend**: React.js + TypeScript
- **Backend**: Node.js + Express
- **Database**: PostgreSQL
- **Real-time**: WebSocket (Socket.io)

### Key Features

```typescript
// Real-time data visualization component
import React, { useEffect, useState } from 'react';
import { Line } from 'react-chartjs-2';
import io from 'socket.io-client';

const RealTimeChart: React.FC = () => {
    const [data, setData] = useState<number[]>([]);
    
    useEffect(() => {
        const socket = io('http://localhost:3000');
        
        socket.on('sensor-data', (newData: number) => {
            setData(prev => [...prev.slice(-99), newData]);
        });
        
        return () => socket.disconnect();
    }, []);
    
    const chartData = {
        labels: data.map((_, i) => i),
        datasets: [{
            label: 'Fluorescence',
            data: data,
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
        }]
    };
    
    return <Line data={chartData} />;
};
```

## Machine Learning Module

### Anomaly Detection

Using isolation forest for outlier detection:

```python
from sklearn.ensemble import IsolationForest
import numpy as np

class AnomalyDetector:
    def __init__(self, contamination=0.1):
        self.model = IsolationForest(contamination=contamination)
        self.is_trained = False
    
    def train(self, X_train):
        self.model.fit(X_train)
        self.is_trained = True
    
    def predict(self, X):
        if not self.is_trained:
            raise ValueError("Model not trained")
        
        # Returns 1 for inliers, -1 for outliers
        predictions = self.model.predict(X)
        return predictions
    
    def score_samples(self, X):
        # Returns anomaly scores
        return self.model.score_samples(X)
```

### Predictive Modeling

LSTM for time series prediction:

```python
import tensorflow as tf
from tensorflow.keras import layers

def create_lstm_model(input_shape):
    model = tf.keras.Sequential([
        layers.LSTM(50, return_sequences=True, 
                   input_shape=input_shape),
        layers.Dropout(0.2),
        layers.LSTM(50, return_sequences=False),
        layers.Dropout(0.2),
        layers.Dense(25),
        layers.Dense(1)
    ])
    
    model.compile(optimizer='adam', 
                  loss='mean_squared_error')
    return model
```

## Image Processing

### Fluorescence Analysis

```python
import cv2
import numpy as np

class FluorescenceAnalyzer:
    def __init__(self):
        self.background = None
        
    def set_background(self, image):
        self.background = cv2.GaussianBlur(image, (5, 5), 0)
    
    def analyze_sample(self, image):
        # Preprocessing
        blurred = cv2.GaussianBlur(image, (5, 5), 0)
        
        # Background subtraction
        if self.background is not None:
            diff = cv2.subtract(blurred, self.background)
        else:
            diff = blurred
        
        # Convert to grayscale if needed
        if len(diff.shape) == 3:
            gray = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
        else:
            gray = diff
        
        # Calculate statistics
        mean_intensity = np.mean(gray)
        max_intensity = np.max(gray)
        
        # Find regions of interest
        _, thresh = cv2.threshold(gray, 0, 255, 
                                  cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        contours, _ = cv2.findContours(thresh, 
                                       cv2.RETR_EXTERNAL, 
                                       cv2.CHAIN_APPROX_SIMPLE)
        
        return {
            'mean_intensity': mean_intensity,
            'max_intensity': max_intensity,
            'roi_count': len(contours)
        }
```

## Database Schema

### PostgreSQL Database Design

```sql
-- Main sensor data table
CREATE TABLE sensor_readings (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP NOT NULL,
    device_id VARCHAR(50) NOT NULL,
    fluorescence FLOAT NOT NULL,
    temperature FLOAT,
    ph FLOAT,
    location POINT,
    metadata JSONB
);

-- Calibration data
CREATE TABLE calibrations (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(50) NOT NULL,
    calibration_date TIMESTAMP NOT NULL,
    slope FLOAT NOT NULL,
    intercept FLOAT NOT NULL,
    r_squared FLOAT,
    valid_until TIMESTAMP
);

-- Alerts and notifications
CREATE TABLE alerts (
    id SERIAL PRIMARY KEY,
    reading_id INTEGER REFERENCES sensor_readings(id),
    alert_type VARCHAR(50),
    threshold_value FLOAT,
    actual_value FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    acknowledged BOOLEAN DEFAULT FALSE
);

-- Indexes for performance
CREATE INDEX idx_readings_timestamp ON sensor_readings(timestamp);
CREATE INDEX idx_readings_device ON sensor_readings(device_id);
CREATE INDEX idx_alerts_unack ON alerts(acknowledged) WHERE acknowledged = FALSE;
```

## API Documentation

### RESTful API Endpoints

```yaml
openapi: 3.0.0
info:
  title: Biosensor API
  version: 1.0.0

paths:
  /api/readings:
    get:
      summary: Get sensor readings
      parameters:
        - name: device_id
          in: query
          schema:
            type: string
        - name: start_date
          in: query
          schema:
            type: string
            format: date-time
        - name: end_date
          in: query
          schema:
            type: string
            format: date-time
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Reading'
    
    post:
      summary: Submit new reading
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Reading'
      responses:
        201:
          description: Created successfully

components:
  schemas:
    Reading:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
        device_id:
          type: string
        fluorescence:
          type: number
        temperature:
          type: number
        ph:
          type: number
```

## Testing Suite

### Unit Testing

```python
import unittest
from biosensor_analyzer import BiosensorAnalyzer

class TestBiosensorAnalyzer(unittest.TestCase):
    def setUp(self):
        self.analyzer = BiosensorAnalyzer('test_data.csv')
    
    def test_preprocessing(self):
        initial_count = len(self.analyzer.data)
        self.analyzer.preprocess()
        # Should remove outliers
        self.assertLess(len(self.analyzer.data), initial_count)
    
    def test_dose_response_fitting(self):
        x = np.array([0, 10, 50, 100, 500, 1000])
        y = np.array([0.1, 0.2, 0.5, 0.7, 0.9, 0.95])
        
        params = self.analyzer.fit_dose_response(x, y)
        # Check if parameters are reasonable
        self.assertGreater(params[1], params[0])  # top > bottom
        self.assertGreater(params[2], 0)  # kd > 0
        self.assertGreater(params[3], 0)  # n > 0
```

## Deployment

### Docker Configuration

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["gunicorn", "--bind", "0.0.0.0:5000", "app:app"]
```

### CI/CD Pipeline

```yaml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run tests
        run: |
          pip install -r requirements.txt
          python -m pytest
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to server
        run: |
          ssh user@server 'cd /app && git pull && docker-compose up -d'
```

## Open Source Contributions

All software is available under MIT License:
- GitHub: [https://github.com/basis-china/biosensor-software](https://github.com/basis-china/biosensor-software)
- Documentation: [https://docs.biosensor.basis-china.org](https://docs.biosensor.basis-china.org)
- PyPI Package: `pip install biosensor-analyzer`