{"name": "basis-china-wiki", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build --mode production", "build:fast": "vite build --mode production", "build:ci": "vite build --mode production --config vite.config.ci.ts", "preview": "vite preview", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .eslint<PERSON>ore", "lint:eslint": "eslint . --fix", "lint": "npm-run-all lint:*", "lint:check": "eslint . && oxlint . -D correctness --ignore-path .eslintignore", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,vue,css,scss,md,json}\" --ignore-path .prettierignore", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,vue,css,scss,md,json}\" --ignore-path .prettierignore", "type-check": "vue-tsc --noEmit", "check-all": "npm run type-check && npm run lint:check && npm run format:check", "fix-all": "npm run lint && npm run format", "test:lint": "npm run check-all", "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist", "clean": "rm -rf dist node_modules/.vite", "prepare": "npm run type-check", "generate:sitemap": "tsx scripts/generate-sitemap.ts", "prebuild": "npm run generate:sitemap", "prebuild:fast": "npm run generate:sitemap"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@types/prismjs": "^1.26.5", "@vueuse/core": "^12.5.0", "ant-design-vue": "^4.2.6", "aos": "^2.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.12.7", "katex": "^0.16.21", "lucide-vue-next": "^0.525.0", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.2.0", "mermaid": "^11.9.0", "pinia": "^2.3.0", "prism-themes": "^1.9.0", "prismjs": "^1.30.0", "radix-vue": "^1.9.17", "tailwind-merge": "^3.3.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/typography": "^0.5.16", "@types/aos": "^3.0.7", "@types/katex": "^0.16.7", "@types/markdown-it": "^13.0.9", "@types/node": "^20.17.24", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-oxlint": "^0.11.1", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "oxlint": "^0.11.1", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "terser": "^5.39.0", "tsx": "^4.20.3", "typescript": "^5.8.2", "typescript-eslint": "^8.28.0", "vite": "^6.0.11", "vite-plugin-vue-devtools": "^7.7.1", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.8"}, "packageManager": "yarn@4.1.1"}